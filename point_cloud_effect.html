<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点云渲染效果</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        canvas {
            border: 1px solid #333;
            background-color: #000;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 10;
        }
        
        .controls button {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px 16px;
            margin: 5px;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .controls button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="generateRabbit()">生成兔子</button>
        <button onclick="generateHeart()">生成爱心</button>
        <button onclick="generateStar()">生成星星</button>
        <button onclick="generateCar()">生成小轿车</button>
        <button onclick="animatePoints()">呼吸动画</button>
    </div>
    
    <canvas id="canvas" width="800" height="600"></canvas>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        let points = [];
        let animationId;
        let breathingPhase = 0;

        // 兔子形状的路径点
        function generateRabbitShape() {
            const rabbitPoints = [];
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 兔子身体 (椭圆)
            for (let angle = 0; angle < Math.PI * 2; angle += 0.1) {
                const x = centerX + Math.cos(angle) * 80;
                const y = centerY + 20 + Math.sin(angle) * 60;
                rabbitPoints.push({x, y});
            }
            
            // 兔子头部 (圆形)
            for (let angle = 0; angle < Math.PI * 2; angle += 0.1) {
                const x = centerX + Math.cos(angle) * 50;
                const y = centerY - 40 + Math.sin(angle) * 45;
                rabbitPoints.push({x, y});
            }
            
            // 左耳朵
            for (let i = 0; i < 30; i++) {
                const t = i / 29;
                const x = centerX - 30 + t * 10;
                const y = centerY - 80 - t * 40;
                rabbitPoints.push({x, y});
            }
            
            // 右耳朵
            for (let i = 0; i < 30; i++) {
                const t = i / 29;
                const x = centerX + 20 + t * 10;
                const y = centerY - 80 - t * 40;
                rabbitPoints.push({x, y});
            }
            
            // 尾巴
            for (let angle = 0; angle < Math.PI * 2; angle += 0.2) {
                const x = centerX + 70 + Math.cos(angle) * 15;
                const y = centerY + 30 + Math.sin(angle) * 15;
                rabbitPoints.push({x, y});
            }
            
            return rabbitPoints;
        }

        // 爱心形状
        function generateHeartShape() {
            const heartPoints = [];
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            for (let t = 0; t < Math.PI * 2; t += 0.1) {
                const x = centerX + 16 * Math.pow(Math.sin(t), 3) * 3;
                const y = centerY - (13 * Math.cos(t) - 5 * Math.cos(2*t) - 2 * Math.cos(3*t) - Math.cos(4*t)) * 3;
                heartPoints.push({x, y});
            }
            
            return heartPoints;
        }

        // 星星形状
        function generateStarShape() {
            const starPoints = [];
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const outerRadius = 80;
            const innerRadius = 40;

            for (let i = 0; i < 10; i++) {
                const angle = (i * Math.PI) / 5;
                const radius = i % 2 === 0 ? outerRadius : innerRadius;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                starPoints.push({x, y});
            }

            return starPoints;
        }

        // 小轿车形状
        function generateCarShape() {
            const carPoints = [];
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 车身主体 (矩形)
            for (let x = -80; x <= 80; x += 2) {
                carPoints.push({x: centerX + x, y: centerY - 20});
                carPoints.push({x: centerX + x, y: centerY + 20});
            }
            for (let y = -20; y <= 20; y += 2) {
                carPoints.push({x: centerX - 80, y: centerY + y});
                carPoints.push({x: centerX + 80, y: centerY + y});
            }

            // 车顶 (较小的矩形)
            for (let x = -50; x <= 50; x += 2) {
                carPoints.push({x: centerX + x, y: centerY - 40});
                carPoints.push({x: centerX + x, y: centerY - 20});
            }
            for (let y = -40; y <= -20; y += 2) {
                carPoints.push({x: centerX - 50, y: centerY + y});
                carPoints.push({x: centerX + 50, y: centerY + y});
            }

            // 左前轮
            for (let angle = 0; angle < Math.PI * 2; angle += 0.2) {
                const x = centerX - 50 + Math.cos(angle) * 15;
                const y = centerY + 35 + Math.sin(angle) * 15;
                carPoints.push({x, y});
            }

            // 右前轮
            for (let angle = 0; angle < Math.PI * 2; angle += 0.2) {
                const x = centerX + 50 + Math.cos(angle) * 15;
                const y = centerY + 35 + Math.sin(angle) * 15;
                carPoints.push({x, y});
            }

            // 车头灯
            carPoints.push({x: centerX + 85, y: centerY - 10});
            carPoints.push({x: centerX + 85, y: centerY + 10});

            return carPoints;
        }

        // 生成点云
        function generatePointCloud(shapePoints, density = 1000) {
            points = [];

            // 基于形状生成密集点云
            shapePoints.forEach(point => {
                for (let i = 0; i < 3; i++) {
                    points.push({
                        x: point.x + (Math.random() - 0.5) * 10,
                        y: point.y + (Math.random() - 0.5) * 10,
                        size: Math.random() * 2 + 0.5,
                        opacity: Math.random() * 0.8 + 0.2,
                        baseOpacity: Math.random() * 0.8 + 0.2,
                        baseSize: Math.random() * 2 + 0.5
                    });
                }
            });

            // 添加随机散布的点
            for (let i = 0; i < density; i++) {
                const basePoint = shapePoints[Math.floor(Math.random() * shapePoints.length)];
                const baseOpacity = Math.random() * 0.6 + 0.1;
                const baseSize = Math.random() * 1.5 + 0.3;
                points.push({
                    x: basePoint.x + (Math.random() - 0.5) * 100,
                    y: basePoint.y + (Math.random() - 0.5) * 100,
                    size: baseSize,
                    opacity: baseOpacity,
                    baseOpacity: baseOpacity,
                    baseSize: baseSize
                });
            }
        }

        // 渲染点云
        function render() {
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            points.forEach(point => {
                ctx.globalAlpha = point.opacity;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(point.x, point.y, point.size, 0, Math.PI * 2);
                ctx.fill();
            });
            
            ctx.globalAlpha = 1;
        }

        // 呼吸动画效果 (0.5Hz = 2秒一个周期)
        function animate() {
            breathingPhase += 0.05; // 控制呼吸频率，约0.5Hz
            const breathingFactor = (Math.sin(breathingPhase) + 1) / 2; // 0到1之间的呼吸因子

            points.forEach(point => {
                // 呼吸效果：透明度和大小随呼吸变化
                point.opacity = point.baseOpacity * (0.3 + 0.7 * breathingFactor);
                point.size = point.baseSize * (0.7 + 0.3 * breathingFactor);
            });

            render();
            animationId = requestAnimationFrame(animate);
        }

        // 控制函数
        function generateRabbit() {
            if (animationId) cancelAnimationFrame(animationId);
            const rabbitShape = generateRabbitShape();
            generatePointCloud(rabbitShape, 800);
            render();
        }

        function generateHeart() {
            if (animationId) cancelAnimationFrame(animationId);
            const heartShape = generateHeartShape();
            generatePointCloud(heartShape, 600);
            render();
        }

        function generateStar() {
            if (animationId) cancelAnimationFrame(animationId);
            const starShape = generateStarShape();
            generatePointCloud(starShape, 500);
            render();
        }

        function generateCar() {
            if (animationId) cancelAnimationFrame(animationId);
            const carShape = generateCarShape();
            generatePointCloud(carShape, 800);
            render();
        }

        function animatePoints() {
            if (points.length === 0) generateRabbit();
            breathingPhase = 0; // 重置呼吸相位
            animate();
        }

        // 初始化
        generateRabbit();
    </script>
</body>
</html>
